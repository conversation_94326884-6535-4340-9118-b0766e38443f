<div>
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Solicitações para Seguir</h2>
    </div>

        @if(count($followRequests) > 0)
            <div class="overflow-hidden">
                <ul role="list" class="divide-y divide-gray-200 dark:divide-zinc-700">
                    @foreach($followRequests as $request)
                        <li class="px-4 py-4 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <a href="/{{ $request->sender->username }}" target="_blank">
                                            @php
                                                $senderAvatar = $request->sender->currentPhoto ?? $request->sender->userPhotos->first();
                                                $senderAvatarUrl = $senderAvatar ? Storage::url($senderAvatar->photo_path) : asset('images/users/avatar.jpg');
                                            @endphp
                                            <img
                                                class="h-10 w-10 rounded-full object-cover"
                                                src="{{ $senderAvatarUrl }}"
                                                alt="{{ $request->sender->name }}"
                                            >
                                        </a>
                                    </div>
                                    <div class="ml-4">
                                        <div class="font-medium text-gray-900 dark:text-white">
                                            <a href="/{{ $request->sender->username }}" class="hover:underline" target="_blank">
                                                {{ $request->sender->name }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            Solicitou seguir você • {{ \Carbon\Carbon::parse($request->created_at)->diffForHumans() }}
                                        </div>
                                    </div>
                                </div>

                                <div class="flex space-x-2">
                                    <button wire:click="accept('{{ $request->id }}')"
                                            class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        Aceitar
                                    </button>

                                    <button wire:click="reject('{{ $request->id }}')"
                                            class="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-zinc-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Rejeitar
                                    </button>

                                    <a href="/{{ $request->sender->username }}"
                                       target="_blank"
                                       class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        Ver perfil
                                    </a>
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        @else
            <div class="text-center py-8">
                <x-flux::icon name="user-plus" class="h-12 w-12 mx-auto text-gray-400" />
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nenhuma solicitação</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Ninguém solicitou para seguir você ainda.
                </p>
        </div>
    @endif
</div>
