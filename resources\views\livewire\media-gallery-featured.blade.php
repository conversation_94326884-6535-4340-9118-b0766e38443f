<div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl shadow-lg overflow-hidden border border-purple-100 dark:border-zinc-700">
    {{-- Header Destacado --}}
    <div class="bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4">
                    <flux:icon.photo class="w-6 h-6" />
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Galeria de {{ $user->name }}</h2>
                    <p class="text-purple-100">
                        {{ $stats['total_medias'] }} {{ $stats['total_medias'] == 1 ? 'mídia' : 'mídias' }} 
                        em {{ $stats['total_albums'] }} {{ $stats['total_albums'] == 1 ? 'álbum' : 'álbuns' }}
                    </p>
                </div>
            </div>
            
            @if(Auth::check() && Auth::id() === $user->id)
                <div class="flex gap-2">
                    <flux:button 
                        wire:click="scrollToFullGallery"
                        variant="ghost" 
                        size="sm"
                        class="text-white border-white hover:bg-white hover:text-purple-600"
                    >
                        <flux:icon.cog-6-tooth class="w-4 h-4 mr-1" />
                        Gerenciar
                    </flux:button>
                </div>
            @endif
        </div>

        {{-- Estatísticas Rápidas --}}
        <div class="grid grid-cols-3 gap-4 mt-6">
            <div class="text-center">
                <div class="text-2xl font-bold">{{ $stats['total_photos'] }}</div>
                <div class="text-sm text-purple-100">Fotos</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold">{{ $stats['total_videos'] }}</div>
                <div class="text-sm text-purple-100">Vídeos</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold">{{ $stats['total_albums'] }}</div>
                <div class="text-sm text-purple-100">Álbuns</div>
            </div>
        </div>
    </div>

    @if($featuredMedias->count() > 0)
        {{-- Grid Principal de Mídias --}}
        <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                @foreach($featuredMedias->take(8) as $index => $media)
                    <div class="relative group cursor-pointer overflow-hidden rounded-lg {{ $index === 0 ? 'md:col-span-2 md:row-span-2' : '' }}"
                         wire:click="openLightbox({{ $media->id }})">
                        
                        {{-- Container da Imagem --}}
                        <div class="aspect-square {{ $index === 0 ? 'md:aspect-video' : '' }} bg-gray-200 dark:bg-zinc-600 overflow-hidden">
                            @if($media->type === 'photo')
                                <img src="{{ $media->thumbnail_url }}" 
                                     alt="{{ $media->title }}"
                                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            @else
                                <div class="w-full h-full bg-gray-800 relative">
                                    <img src="{{ $media->thumbnail_url }}" 
                                         alt="{{ $media->title }}"
                                         class="w-full h-full object-cover opacity-80 group-hover:scale-110 transition-transform duration-500">
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="w-12 h-12 bg-black bg-opacity-60 rounded-full flex items-center justify-center">
                                            <flux:icon.play class="w-6 h-6 text-white ml-1" />
                                        </div>
                                    </div>
                                    @if($media->formatted_duration)
                                        <div class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                                            {{ $media->formatted_duration }}
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </div>

                        {{-- Overlay com Informações --}}
                        <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-3 left-3 right-3">
                                @if($media->title)
                                    <h4 class="text-white font-medium text-sm truncate">{{ $media->title }}</h4>
                                @endif
                                <p class="text-gray-300 text-xs">{{ $media->album->name }}</p>
                            </div>
                        </div>

                        {{-- Badge de Destaque para a primeira mídia --}}
                        @if($index === 0)
                            <div class="absolute top-3 left-3">
                                <flux:badge variant="primary" class="bg-purple-600 text-white">
                                    <flux:icon.star class="w-3 h-3 mr-1" />
                                    Destaque
                                </flux:badge>
                            </div>
                        @endif
                    </div>
                @endforeach

                {{-- Botão "Ver Mais" se houver mais mídias --}}
                @if($featuredMedias->count() > 8)
                    <div class="aspect-square bg-gradient-to-br from-purple-100 to-pink-100 dark:from-zinc-700 dark:to-zinc-800 rounded-lg flex items-center justify-center cursor-pointer hover:from-purple-200 hover:to-pink-200 dark:hover:from-zinc-600 dark:hover:to-zinc-700 transition-colors group"
                         wire:click="scrollToFullGallery">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform">
                                <flux:icon.plus class="w-6 h-6 text-white" />
                            </div>
                            <div class="text-purple-600 dark:text-purple-400 font-medium text-sm">
                                +{{ $featuredMedias->count() - 8 }}
                            </div>
                            <div class="text-purple-500 dark:text-purple-500 text-xs">
                                Ver mais
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        {{-- Seção de Álbuns Recentes --}}
        @if($recentAlbums->count() > 0)
            <div class="border-t border-purple-100 dark:border-zinc-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <flux:icon.folder class="w-5 h-5 mr-2 text-purple-500" />
                        Álbuns Recentes
                    </h3>
                    @if($stats['total_albums'] > 4)
                        <button wire:click="scrollToFullGallery" class="text-purple-600 hover:text-purple-700 dark:text-purple-400 text-sm font-medium">
                            Ver todos ({{ $stats['total_albums'] }})
                        </button>
                    @endif
                </div>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    @foreach($recentAlbums as $album)
                        <div class="bg-white dark:bg-zinc-800 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer group"
                             wire:click="scrollToFullGallery">
                            {{-- Preview do Álbum --}}
                            <div class="aspect-video bg-gray-200 dark:bg-zinc-600 relative overflow-hidden">
                                @if($album->medias->count() > 0)
                                    <div class="grid {{ $album->medias->count() === 1 ? 'grid-cols-1' : 'grid-cols-2' }} gap-0.5 h-full">
                                        @foreach($album->medias->take(3) as $media)
                                            <div class="relative {{ $loop->first && $album->medias->count() === 1 ? 'col-span-2' : '' }}">
                                                @if($media->type === 'photo')
                                                    <img src="{{ $media->thumbnail_url }}" 
                                                         alt="{{ $media->title }}"
                                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                                @else
                                                    <div class="w-full h-full bg-gray-800 flex items-center justify-center">
                                                        <flux:icon.play class="w-4 h-4 text-white" />
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="w-full h-full flex items-center justify-center">
                                        <flux:icon.folder class="w-8 h-8 text-gray-400" />
                                    </div>
                                @endif
                                
                                {{-- Contador --}}
                                <div class="absolute top-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                                    {{ $album->medias_count }}
                                </div>
                            </div>
                            
                            {{-- Info do Álbum --}}
                            <div class="p-3">
                                <h4 class="font-medium text-gray-900 dark:text-white text-sm truncate">
                                    {{ $album->name }}
                                </h4>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                    {{ $album->medias_count }} {{ $album->medias_count == 1 ? 'item' : 'itens' }}
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        {{-- Botão de Ação Principal --}}
        <div class="border-t border-purple-100 dark:border-zinc-700 p-6 bg-gray-50 dark:bg-zinc-800">
            <div class="flex items-center justify-center">
                <flux:button 
                    wire:click="scrollToFullGallery"
                    variant="primary"
                    size="lg"
                    class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-none shadow-lg"
                >
                    <flux:icon.photo class="w-5 h-5 mr-2" />
                    Explorar Galeria Completa
                    <flux:icon.arrow-right class="w-5 h-5 ml-2" />
                </flux:button>
            </div>
        </div>
    @else
        {{-- Estado Vazio --}}
        <div class="p-12 text-center">
            <div class="w-20 h-20 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <flux:icon.photo class="w-10 h-10 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {{ Auth::check() && Auth::id() === $user->id ? 'Sua galeria está vazia' : 'Galeria vazia' }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                {{ Auth::check() && Auth::id() === $user->id ? 'Comece adicionando suas primeiras fotos e vídeos para criar uma galeria incrível!' : 'Este usuário ainda não adicionou fotos ou vídeos públicos.' }}
            </p>
            @if(Auth::check() && Auth::id() === $user->id)
                <flux:button 
                    wire:click="scrollToFullGallery"
                    variant="primary"
                    class="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-none"
                >
                    <flux:icon.plus class="w-4 h-4 mr-2" />
                    Adicionar Primeira Mídia
                </flux:button>
            @endif
        </div>
    @endif

    {{-- Lightbox Avançado --}}
    @if($showLightbox && $selectedMedia)
        <div class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
             wire:click="closeLightbox"
             x-data="{ loading: true }"
             x-init="setTimeout(() => loading = false, 100)">

            {{-- Controles do Lightbox --}}
            <div class="absolute top-4 left-4 right-4 flex items-center justify-between z-10">
                <div class="flex items-center text-white">
                    <div class="w-10 h-10 bg-black bg-opacity-50 rounded-full flex items-center justify-center mr-3">
                        @if($selectedMedia->type === 'photo')
                            <flux:icon.photo class="w-5 h-5" />
                        @else
                            <flux:icon.video-camera class="w-5 h-5" />
                        @endif
                    </div>
                    <div>
                        <h4 class="font-medium">{{ $selectedMedia->title ?: 'Mídia' }}</h4>
                        <p class="text-sm text-gray-300">{{ $selectedMedia->album->name }} • {{ $currentIndex + 1 }} de {{ $featuredMedias->count() }}</p>
                    </div>
                </div>

                <flux:button
                    wire:click="closeLightbox"
                    variant="ghost"
                    size="sm"
                    class="text-white hover:bg-white hover:bg-opacity-20"
                >
                    <flux:icon.x-mark class="w-6 h-6" />
                </flux:button>
            </div>

            {{-- Navegação Anterior --}}
            @if($currentIndex > 0)
                <button wire:click.stop="previousMedia"
                        class="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full flex items-center justify-center text-white transition-all z-10">
                    <flux:icon.chevron-left class="w-6 h-6" />
                </button>
            @endif

            {{-- Navegação Próxima --}}
            @if($currentIndex < $featuredMedias->count() - 1)
                <button wire:click.stop="nextMedia"
                        class="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full flex items-center justify-center text-white transition-all z-10">
                    <flux:icon.chevron-right class="w-6 h-6" />
                </button>
            @endif

            {{-- Conteúdo Principal --}}
            <div class="max-w-6xl max-h-full p-4 flex items-center justify-center" wire:click.stop>
                @if($selectedMedia->type === 'photo')
                    <img src="{{ $selectedMedia->url }}"
                         alt="{{ $selectedMedia->title }}"
                         class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                         x-show="!loading"
                         x-transition:enter="transition ease-out duration-300"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100">
                @else
                    <video src="{{ $selectedMedia->url }}"
                           controls
                           autoplay
                           class="max-w-full max-h-full rounded-lg shadow-2xl"
                           x-show="!loading"
                           x-transition:enter="transition ease-out duration-300"
                           x-transition:enter-start="opacity-0 scale-95"
                           x-transition:enter-end="opacity-100 scale-100">
                    </video>
                @endif

                {{-- Loading Spinner --}}
                <div x-show="loading" class="flex items-center justify-center">
                    <div class="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
            </div>

            {{-- Informações da Mídia --}}
            <div class="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 rounded-lg p-4 text-white">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="text-gray-300">Tipo:</span>
                        <span class="ml-2">{{ $selectedMedia->type === 'photo' ? 'Foto' : 'Vídeo' }}</span>
                    </div>
                    <div>
                        <span class="text-gray-300">Tamanho:</span>
                        <span class="ml-2">{{ $selectedMedia->formatted_size }}</span>
                    </div>
                    @if($selectedMedia->width && $selectedMedia->height)
                        <div>
                            <span class="text-gray-300">Dimensões:</span>
                            <span class="ml-2">{{ $selectedMedia->width }}x{{ $selectedMedia->height }}</span>
                        </div>
                    @endif
                </div>

                @if($selectedMedia->description)
                    <div class="mt-3 pt-3 border-t border-gray-600">
                        <p class="text-gray-200">{{ $selectedMedia->description }}</p>
                    </div>
                @endif
            </div>

            {{-- Thumbnails de Navegação --}}
            <div class="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex gap-2 max-w-md overflow-x-auto">
                @foreach($featuredMedias->take(10) as $index => $media)
                    <button wire:click.stop="openLightbox({{ $media->id }})"
                            class="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 border-2 {{ $currentIndex === $index ? 'border-white' : 'border-transparent' }} opacity-{{ $currentIndex === $index ? '100' : '70' }} hover:opacity-100 transition-all">
                        @if($media->type === 'photo')
                            <img src="{{ $media->thumbnail_url }}"
                                 alt="{{ $media->title }}"
                                 class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full bg-gray-800 flex items-center justify-center">
                                <flux:icon.play class="w-4 h-4 text-white" />
                            </div>
                        @endif
                    </button>
                @endforeach
            </div>
        </div>
    @endif
</div>

{{-- Script para scroll suave --}}
<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('scroll-to-element', (event) => {
            const element = document.querySelector(event.element);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
</script>
