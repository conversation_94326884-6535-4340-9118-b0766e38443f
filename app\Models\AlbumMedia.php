<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AlbumMedia extends Model
{
    use HasFactory;

    protected $fillable = [
        'album_id',
        'user_id',
        'type',
        'file_path',
        'thumbnail_path',
        'title',
        'description',
        'file_size',
        'mime_type',
        'width',
        'height',
        'duration',
        'sort_order',
        'is_cover',
        'uploaded_at',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'duration' => 'integer',
        'sort_order' => 'integer',
        'is_cover' => 'boolean',
        'uploaded_at' => 'datetime',
    ];

    /**
     * Relacionamento com o álbum
     */
    public function album(): BelongsTo
    {
        return $this->belongsTo(Album::class);
    }

    /**
     * Relacionamento com o usuário
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * URL do arquivo
     */
    public function getUrlAttribute()
    {
        return \Storage::url($this->file_path);
    }

    /**
     * URL da thumbnail
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail_path) {
            return \Storage::url($this->thumbnail_path);
        }

        // Para fotos, usar a própria imagem
        if ($this->type === 'photo') {
            return $this->url;
        }

        // Para vídeos, usar thumbnail padrão
        return asset('images/video-thumbnail.jpg');
    }

    /**
     * Tamanho do arquivo formatado
     */
    public function getFormattedSizeAttribute()
    {
        if (!$this->file_size) {
            return 'Desconhecido';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Duração formatada (para vídeos)
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->duration) {
            return null;
        }

        $minutes = floor($this->duration / 60);
        $seconds = $this->duration % 60;
        
        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * Verifica se é uma foto
     */
    public function isPhoto(): bool
    {
        return $this->type === 'photo';
    }

    /**
     * Verifica se é um vídeo
     */
    public function isVideo(): bool
    {
        return $this->type === 'video';
    }

    /**
     * Scope para fotos
     */
    public function scopePhotos($query)
    {
        return $query->where('type', 'photo');
    }

    /**
     * Scope para vídeos
     */
    public function scopeVideos($query)
    {
        return $query->where('type', 'video');
    }

    /**
     * Scope ordenado
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($media) {
            // Definir ordem se não especificada
            if (!$media->sort_order) {
                $maxOrder = static::where('album_id', $media->album_id)->max('sort_order') ?? 0;
                $media->sort_order = $maxOrder + 1;
            }

            // Definir data de upload
            if (!$media->uploaded_at) {
                $media->uploaded_at = now();
            }
        });

        static::created(function ($media) {
            // Adicionar pontos por upload de mídia
            $points = $media->type === 'video' ? 15 : 10;
            \App\Models\UserPoint::addPoints(
                $media->user_id,
                'media_uploaded',
                $points,
                "Adicionou " . ($media->type === 'video' ? 'vídeo' : 'foto') . " ao álbum",
                $media->id,
                AlbumMedia::class
            );
        });
    }
}
